/**
 * ENGLISH 2025 - Mock Data
 * Comprehensive sample data for prototype development
 */

class MockDataManager {
    constructor() {
        this.storageKey = 'esl_mock_data';
        this.init();
    }

    /**
     * Initialize mock data
     */
    init() {
        if (!localStorage.getItem(this.storageKey)) {
            this.generateMockData();
        }
    }

    /**
     * Generate comprehensive mock data
     */
    generateMockData() {
        const mockData = {
            courses: this.generateCourses(),
            lessons: this.generateLessons(),
            exercises: this.generateExercises(),
            schedules: this.generateSchedules(),
            metrics: this.generateMetrics(),
            notifications: this.generateNotifications(),
            certificates: this.generateCertificates()
        };

        localStorage.setItem(this.storageKey, JSON.stringify(mockData));
    }

    /**
     * Generate course data
     */
    generateCourses() {
        return {
            levels: [
                {
                    id: 'A1',
                    name: 'Beginner (A1)',
                    description: 'Basic English for everyday situations',
                    units: [
                        {
                            id: 'A1_Unit1',
                            name: 'Greetings & Introductions',
                            lessons: 8,
                            duration: '2 weeks',
                            topics: ['Hello & Goodbye', 'Personal Information', 'Numbers', 'Time']
                        },
                        {
                            id: 'A1_Unit2',
                            name: 'Daily Activities',
                            lessons: 10,
                            duration: '2.5 weeks',
                            topics: ['Daily Routine', 'Food & Drinks', 'Shopping', 'Transportation']
                        }
                    ]
                },
                {
                    id: 'A2',
                    name: 'Elementary (A2)',
                    description: 'Building confidence in basic communication',
                    units: [
                        {
                            id: 'A2_Unit1',
                            name: 'Family & Relationships',
                            lessons: 12,
                            duration: '3 weeks',
                            topics: ['Family Members', 'Describing People', 'Relationships', 'Emotions']
                        },
                        {
                            id: 'A2_Unit2',
                            name: 'Work & Study',
                            lessons: 14,
                            duration: '3.5 weeks',
                            topics: ['Jobs & Professions', 'School Subjects', 'Skills', 'Future Plans']
                        }
                    ]
                },
                {
                    id: 'B1',
                    name: 'Intermediate (B1)',
                    description: 'Developing fluency and confidence',
                    units: [
                        {
                            id: 'B1_Unit1',
                            name: 'Travel & Culture',
                            lessons: 16,
                            duration: '4 weeks',
                            topics: ['Travel Planning', 'Cultural Differences', 'Experiences', 'Opinions']
                        },
                        {
                            id: 'B1_Unit2',
                            name: 'Health & Lifestyle',
                            lessons: 18,
                            duration: '4.5 weeks',
                            topics: ['Health Problems', 'Exercise', 'Healthy Living', 'Medical Advice']
                        }
                    ]
                }
            ]
        };
    }

    /**
     * Generate lesson data
     */
    generateLessons() {
        return [
            {
                id: 'A1_Unit1_Lesson1',
                title: 'Hello & Goodbye',
                level: 'A1',
                unit: 'A1_Unit1',
                duration: 30,
                type: 'vocabulary',
                content: {
                    vocabulary: [
                        { word: 'hello', pronunciation: '/həˈloʊ/', meaning: 'greeting' },
                        { word: 'goodbye', pronunciation: '/ɡʊdˈbaɪ/', meaning: 'farewell' },
                        { word: 'please', pronunciation: '/pliːz/', meaning: 'polite request' },
                        { word: 'thank you', pronunciation: '/θæŋk juː/', meaning: 'gratitude' }
                    ],
                    dialogues: [
                        {
                            speakers: ['A', 'B'],
                            lines: [
                                { speaker: 'A', text: 'Hello! How are you?' },
                                { speaker: 'B', text: 'I\'m fine, thank you. And you?' },
                                { speaker: 'A', text: 'I\'m good, thanks!' }
                            ]
                        }
                    ]
                },
                exercises: ['exercise_001', 'exercise_002'],
                homework: 'homework_001'
            }
        ];
    }

    /**
     * Generate exercise data
     */
    generateExercises() {
        return [
            {
                id: 'exercise_001',
                type: 'multiple_choice',
                question: 'What is the correct greeting?',
                options: ['Hello', 'Goodbye', 'Please', 'Sorry'],
                correct: 0,
                points: 10
            },
            {
                id: 'exercise_002',
                type: 'fill_blank',
                question: 'Complete: "_____ you very much!"',
                answer: 'Thank',
                points: 15
            }
        ];
    }

    /**
     * Generate schedule data
     */
    generateSchedules() {
        const today = new Date();
        const schedules = [];

        for (let i = 0; i < 30; i++) {
            const date = new Date(today);
            date.setDate(today.getDate() + i);
            
            schedules.push({
                id: `schedule_${i}`,
                date: date.toISOString().split('T')[0],
                slots: [
                    {
                        time: '09:00',
                        teacher: 'Sarah Johnson',
                        available: Math.random() > 0.3,
                        studentId: Math.random() > 0.5 ? 'student_001' : null
                    },
                    {
                        time: '10:00',
                        teacher: 'Sarah Johnson',
                        available: Math.random() > 0.3,
                        studentId: Math.random() > 0.5 ? 'student_002' : null
                    },
                    {
                        time: '14:00',
                        teacher: 'Sarah Johnson',
                        available: Math.random() > 0.3,
                        studentId: null
                    },
                    {
                        time: '15:00',
                        teacher: 'Sarah Johnson',
                        available: Math.random() > 0.3,
                        studentId: null
                    }
                ]
            });
        }

        return schedules;
    }

    /**
     * Generate metrics data
     */
    generateMetrics() {
        return {
            platform: {
                totalUsers: 1247,
                activeUsers: 892,
                newUsersThisMonth: 156,
                retentionRate: 78.5,
                averageSessionTime: 45,
                lessonsCompleted: 3456,
                certificatesIssued: 234
            },
            revenue: {
                monthlyRevenue: 24580,
                yearlyRevenue: 285600,
                averageRevenuePerUser: 19.7,
                subscriptionTypes: {
                    '10_classes': 456,
                    '20_classes': 623,
                    'unlimited': 168
                }
            },
            engagement: {
                dailyActiveUsers: [120, 135, 142, 128, 156, 134, 98],
                weeklyActiveUsers: [892, 856, 923, 867, 945, 912, 889],
                lessonCompletionRate: 85.3,
                averageScore: 82.7,
                studyStreakAverage: 5.2
            },
            teachers: {
                totalTeachers: 45,
                activeTeachers: 38,
                averageRating: 4.7,
                totalClassesThisMonth: 2340,
                averageClassesPerTeacher: 52
            }
        };
    }

    /**
     * Generate notification data
     */
    generateNotifications() {
        return [
            {
                id: 'notif_001',
                type: 'class_reminder',
                title: 'Class Reminder',
                message: 'Your conversation class with Sarah Johnson starts in 30 minutes',
                timestamp: new Date().toISOString(),
                read: false,
                priority: 'high'
            },
            {
                id: 'notif_002',
                type: 'achievement',
                title: 'Achievement Unlocked!',
                message: 'Congratulations! You completed Unit 1 with 85% score',
                timestamp: new Date(Date.now() - 3600000).toISOString(),
                read: false,
                priority: 'medium'
            },
            {
                id: 'notif_003',
                type: 'homework',
                title: 'Homework Due',
                message: 'Don\'t forget to submit your pronunciation exercise by tomorrow',
                timestamp: new Date(Date.now() - 7200000).toISOString(),
                read: true,
                priority: 'medium'
            }
        ];
    }

    /**
     * Generate certificate data
     */
    generateCertificates() {
        return [
            {
                id: 'cert_001',
                studentId: 'student_001',
                level: 'A1',
                issuedDate: '2024-01-15',
                score: 85,
                skills: {
                    listening: 82,
                    speaking: 88,
                    reading: 85,
                    writing: 84
                }
            },
            {
                id: 'cert_002',
                studentId: 'student_001',
                level: 'A2',
                issuedDate: '2024-03-20',
                score: 92,
                skills: {
                    listening: 90,
                    speaking: 94,
                    reading: 91,
                    writing: 93
                }
            }
        ];
    }

    /**
     * Get mock data by category
     * @param {string} category - Data category
     * @returns {*} Mock data for category
     */
    getData(category) {
        try {
            const data = JSON.parse(localStorage.getItem(this.storageKey));
            return data ? data[category] : null;
        } catch (error) {
            console.error('Error getting mock data:', error);
            return null;
        }
    }

    /**
     * Update mock data
     * @param {string} category - Data category
     * @param {*} newData - New data
     * @returns {boolean} Success status
     */
    updateData(category, newData) {
        try {
            const data = JSON.parse(localStorage.getItem(this.storageKey)) || {};
            data[category] = newData;
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error updating mock data:', error);
            return false;
        }
    }

    /**
     * Reset all mock data
     */
    reset() {
        localStorage.removeItem(this.storageKey);
        this.generateMockData();
    }
}

// Create global instance
window.mockDataManager = new MockDataManager();
